<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from '../../composables/useI18n'


const route = useRoute()
const { locale, setLocale, t } = useI18n()

const items = computed(() => [{
  label: t('nav.docs'),
  to: '/docs',
  active: route.path.startsWith('/docs')
}, {
  label: t('nav.pricing'),
  to: '/pricing'
}, {
  label: t('nav.blog'),
  to: '/blog'
}, {
  label: t('nav.changelog'),
  to: '/changelog',
  badge: {
    label: t('nav.new'),
    color: 'primary' as const
  }
}])
</script>

<template>
  <UHeader>
    <template #left>
      <NuxtLink to="/">
        <LogoPro class="w-auto h-6 shrink-0" />
      </NuxtLink>
      <TemplateMenu />
    </template>

    <UNavigationMenu
      :items="items"
      variant="link"
    />

    <template #right>
      <UColorModeButton />

      <div class="flex items-center gap-1">
        <UButton
          variant="ghost"
          color="neutral"
          size="xs"
          class="rounded-full w-8 h-8 p-0 flex items-center justify-center"
          :class="locale === 'th' ? 'bg-primary/15' : ''"
          aria-label="ภาษาไทย"
          title="ภาษาไทย"
          @click="setLocale('th')"
        >
          <span class="text-base">🇹🇭</span>
        </UButton>
        <UButton
          variant="ghost"
          color="neutral"
          size="xs"
          class="rounded-full w-8 h-8 p-0 flex items-center justify-center"
          :class="locale === 'en' ? 'bg-primary/15' : ''"
          aria-label="English"
          title="English"
          @click="setLocale('en')"
        >
          <span class="text-base">🇬🇧</span>
        </UButton>
      </div>

      <UButton
        icon="i-lucide-log-in"
        color="neutral"
        variant="ghost"
        to="/login"
        class="lg:hidden"
      />

      <UButton
        :label="t('auth.signIn')"
        color="neutral"
        variant="outline"
        to="/login"
        class="hidden lg:inline-flex"
      />

      <UButton
        :label="t('auth.signUp')"
        color="neutral"
        trailing-icon="i-lucide-arrow-right"
        class="hidden lg:inline-flex"
        to="/signup"
      />
    </template>

    <template #body>
      <UNavigationMenu
        :items="items"
        orientation="vertical"
        class="-mx-2.5"
      />

      <USeparator class="my-6" />

      <UButton
        :label="t('auth.signIn')"
        color="neutral"
        variant="subtle"
        to="/login"
        block
        class="mb-3"
      />
      <UButton
        :label="t('auth.signUp')"
        color="neutral"
        to="/signup"
        block
      />
    </template>
  </UHeader>
</template>
