import { computed, watch } from 'vue'

// Lightweight i18n composable for header/UI strings
// Can be replaced by @nuxtjs/i18n later without changing call sites
export const useI18n = () => {
  const cookie = useCookie<string>('locale', { sameSite: 'lax' })
  const locale = useState<string>('app-locale', () => cookie.value || 'en')

  watch(locale, (val) => {
    cookie.value = val
  }, { immediate: true })

  const messages = {
    en: {
      nav: {
        docs: 'Docs',
        pricing: 'Pricing',
        blog: 'Blog',
        changelog: 'Changelog',
        new: 'New'
      },
      auth: {
        signIn: 'Sign in',
        signUp: 'Sign up'
      }
    },
    th: {
      nav: {
        docs: 'เอกสาร',
        pricing: 'ราคา',
        blog: 'บล็อก',
        changelog: 'บันทึกการเปลี่ยนแปลง',
        new: 'ใหม่'
      },
      auth: {
        signIn: 'เข้าสู่ระบบ',
        signUp: 'สมัครสมาชิก'
      }
    }
  } as const

  function setLocale(lang: 'en' | 'th') {
    locale.value = lang
  }

  function t(path: string): string {
    const parts = path.split('.')
    let cur: any = messages[locale.value as 'en' | 'th']
    for (const p of parts) {
      cur = cur?.[p]
      if (cur == null) return path
    }
    return String(cur)
  }

  const htmlLang = computed(() => locale.value)

  return { locale, setLocale, t, htmlLang }
}

